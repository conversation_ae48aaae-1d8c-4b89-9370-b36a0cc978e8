
逾期概率预测模型 - 项目总结报告
生成时间: 2025-06-17 15:02:16

数据概况:
- 总样本数: 2,374
- 原始特征数: 1226
- 最终特征数: 50
- 正样本比例: 92.71%
- 训练集大小: 1,661
- 测试集大小: 475

模型性能:
- 最佳模型: lightgbm
- 测试集AUC: 0.9992
- 测试集PR-AUC: 0.9999
- 最优阈值: 0.800
- 精确率: 1.000
- 召回率: 0.998
- F1分数: 0.999

特征工程:
- 特征选择方法: LightGBM重要性 + 交叉验证
- 避免数据泄露: ✓ (仅在训练集上进行特征选择)
- 处理不平衡数据: ✓ (使用is_unbalance=True)
- 缺失值处理: ✓ (数值型用中位数，分类型用众数)

业务价值:
- 最小化业务成本: 1,000
- 误判成本节约: 通过优化阈值减少误判
- 风险识别能力: 能够有效识别高风险客户

输出文件:
- overdue_prediction_model.pkl - 完整模型包
- selected_features.txt - 特征列表
- test_predictions.csv - 测试集预测结果

注意事项:
- 模型需要定期重训练以保持性能
- 特征稳定性需要持续监控
- 业务阈值可根据实际成本调整
- 建议进行A/B测试验证模型效果
