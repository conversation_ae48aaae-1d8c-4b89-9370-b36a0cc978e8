# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime
import joblib

# 机器学习相关
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.preprocessing import LabelEncoder, StandardScaler, RobustScaler
from sklearn.impute import SimpleImputer
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.metrics import (
    roc_auc_score, roc_curve, precision_recall_curve, 
    classification_report, confusion_matrix, f1_score,
    average_precision_score, accuracy_score
)
from sklearn.calibration import calibration_curve, CalibratedClassifierCV

# 模型
import lightgbm as lgb
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression

# 模型解释
import shap

# 设置
RANDOM_STATE = 42
np.random.seed(RANDOM_STATE)
warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

print("✓ 库导入完成")

# 数据加载函数
def load_and_preprocess_data(file_path, target_col=None):
    """
    加载并预处理数据
    """
    print("=== 数据加载与预处理 ===")
    
    # 加载数据
    try:
        data = pd.read_csv(file_path)
        print(f"✓ 数据加载成功，形状: {data.shape}")
    except FileNotFoundError:
        print(f"❌ 文件 {file_path} 不存在")
        # 创建示例数据用于演示
        print("创建示例数据...")
        np.random.seed(RANDOM_STATE)
        n_samples = 2000
        n_features = 100
        
        # 生成特征数据
        X = np.random.randn(n_samples, n_features)
        # 添加一些相关性
        X[:, 1] = X[:, 0] + np.random.randn(n_samples) * 0.5
        X[:, 2] = X[:, 0] * 2 + np.random.randn(n_samples) * 0.3
        
        # 生成目标变量（不平衡）
        linear_combination = (X[:, 0] * 0.5 + X[:, 1] * 0.3 + X[:, 2] * 0.2 + 
                            X[:, 3] * 0.1 + np.random.randn(n_samples) * 0.5)
        y = (linear_combination > np.percentile(linear_combination, 85)).astype(int)
        
        # 创建DataFrame
        feature_names = [f'feature_{i:03d}' for i in range(n_features)]
        data = pd.DataFrame(X, columns=feature_names)
        data['target'] = y
        
        # 添加一些缺失值和特殊值
        missing_mask = np.random.random(data.shape) < 0.05
        data = data.mask(missing_mask)
        
        # 添加一些-999特殊值
        special_mask = np.random.random(data.iloc[:, :-1].shape) < 0.02
        data.iloc[:, :-1] = data.iloc[:, :-1].mask(special_mask, -999)
        
        print(f"✓ 示例数据创建完成，形状: {data.shape}")
        target_col = 'target'
    
    # 自动检测目标变量
    if target_col is None:
        target_candidates = ['target', 'label', 'overdue', 'default', 'y', 'is_overdue', '好坏标签']
        for col in target_candidates:
            if col in data.columns:
                target_col = col
                break
        
        if target_col is None:
            raise ValueError("无法自动检测目标变量，请手动指定")
    
    print(f"✓ 目标变量: {target_col}")
    print(f"✓ 目标变量分布: {dict(data[target_col].value_counts())}")
    
    # 转换目标变量为数值型（如果是中文标签）
    if data[target_col].dtype == 'object':
        print("\n转换目标变量为数值型...")
        unique_values = data[target_col].unique()
        print(f"原始标签: {unique_values}")
        
        # 创建映射规则：坏->0, 好->1
        if '坏' in unique_values and '好' in unique_values:
            label_mapping = {'坏': 0, '好': 1}
            print("使用映射: 坏->0, 好->1")
        elif 'bad' in [str(v).lower() for v in unique_values] and 'good' in [str(v).lower() for v in unique_values]:
            # 处理英文标签
            label_mapping = {}
            for val in unique_values:
                if str(val).lower() in ['bad', '0', 'false']:
                    label_mapping[val] = 0
                elif str(val).lower() in ['good', '1', 'true']:
                    label_mapping[val] = 1
            print(f"使用映射: {label_mapping}")
        else:
            # 自动映射：第一个值->0，第二个值->1
            sorted_values = sorted(unique_values)
            label_mapping = {sorted_values[0]: 0, sorted_values[1]: 1}
            print(f"自动映射: {label_mapping}")
        
        # 应用映射
        data[target_col] = data[target_col].map(label_mapping)
        print(f"✓ 目标变量转换完成")
        print(f"✓ 转换后分布: {dict(data[target_col].value_counts())}")
    
    return data, target_col

# 加载数据
data, target_col = load_and_preprocess_data('result_data.csv')

# 基本数据清理
def basic_data_cleaning(data, target_col):
    """
    基本数据清理
    """
    print("=== 基本数据清理 ===")
    
    # 分离特征和目标
    y = data[target_col].copy()
    X = data.drop(columns=[target_col])
    
    print(f"原始特征数: {X.shape[1]}")
    
    # 1. 处理特殊值
    special_values = [-999, -1, 999, 9999]
    for val in special_values:
        replaced_count = (X == val).sum().sum()
        if replaced_count > 0:
            X = X.replace(val, np.nan)
            print(f"✓ 将 {replaced_count} 个 {val} 替换为 NaN")
    
    # 2. 删除高缺失率列（>80%）
    missing_ratios = X.isnull().sum() / len(X)
    high_missing_cols = missing_ratios[missing_ratios > 0.8].index.tolist()
    if high_missing_cols:
        X = X.drop(columns=high_missing_cols)
        print(f"✓ 删除 {len(high_missing_cols)} 个高缺失率列")
    
    # 3. 删除常数列
    constant_cols = []
    for col in X.select_dtypes(include=[np.number]).columns:
        if X[col].nunique() <= 1:
            constant_cols.append(col)
    
    if constant_cols:
        X = X.drop(columns=constant_cols)
        print(f"✓ 删除 {len(constant_cols)} 个常数列")
    
    print(f"清理后特征数: {X.shape[1]}")
    
    return X, y

# 执行基本清理
X_clean, y = basic_data_cleaning(data, target_col)

# 数据集划分
def split_data(X, y, test_size=0.2, val_size=0.1, random_state=42):
    """
    划分数据集
    """
    print("=== 数据集划分 ===")
    print(f"原始数据: {X.shape[0]} 样本, {X.shape[1]} 特征")
    
    # 首先划分训练集和测试集
    X_temp, X_test, y_temp, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )
    
    # 从临时训练集中划分验证集
    if val_size > 0:
        val_size_adjusted = val_size / (1 - test_size)
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_size_adjusted, 
            random_state=random_state, stratify=y_temp
        )
    else:
        X_train, y_train = X_temp, y_temp
        X_val, y_val = None, None
    
    print(f"训练集: {X_train.shape[0]} 样本 ({X_train.shape[0]/X.shape[0]*100:.1f}%)")
    if X_val is not None:
        print(f"验证集: {X_val.shape[0]} 样本 ({X_val.shape[0]/X.shape[0]*100:.1f}%)")
    print(f"测试集: {X_test.shape[0]} 样本 ({X_test.shape[0]/X.shape[0]*100:.1f}%)")
    
    # 检查类别分布
    print(f"\n类别分布:")
    print(f"训练集: {dict(y_train.value_counts())}")
    if y_val is not None:
        print(f"验证集: {dict(y_val.value_counts())}")
    print(f"测试集: {dict(y_test.value_counts())}")
    
    return X_train, X_val, X_test, y_train, y_val, y_test

# 执行数据集划分
X_train, X_val, X_test, y_train, y_val, y_test = split_data(
    X_clean, y, test_size=0.2, val_size=0.1, random_state=RANDOM_STATE
)

# 缺失值处理和特征编码
def preprocess_features(X_train, X_val, X_test):
    """
    处理缺失值和特征编码
    """
    print("=== 特征预处理 ===")
    
    # 复制数据
    X_train_processed = X_train.copy()
    X_val_processed = X_val.copy() if X_val is not None else None
    X_test_processed = X_test.copy()
    
    # 分离数值型和分类型特征
    numeric_features = X_train.select_dtypes(include=[np.number]).columns.tolist()
    categorical_features = X_train.select_dtypes(exclude=[np.number]).columns.tolist()
    
    print(f"数值型特征: {len(numeric_features)}")
    print(f"分类型特征: {len(categorical_features)}")
    
    # 处理数值型特征的缺失值
    if numeric_features:
        print("\n处理数值型特征缺失值...")
        numeric_imputer = SimpleImputer(strategy='median')
        
        X_train_processed[numeric_features] = numeric_imputer.fit_transform(
            X_train_processed[numeric_features]
        )
        
        if X_val_processed is not None:
            X_val_processed[numeric_features] = numeric_imputer.transform(
                X_val_processed[numeric_features]
            )
        
        X_test_processed[numeric_features] = numeric_imputer.transform(
            X_test_processed[numeric_features]
        )
        
        print(f"✓ 数值型特征缺失值处理完成")
    
    # 处理分类型特征
    encoders = {}
    if categorical_features:
        print("\n处理分类型特征...")
        
        for col in categorical_features:
            # 缺失值填充
            cat_imputer = SimpleImputer(strategy='most_frequent')
            X_train_processed[col] = cat_imputer.fit_transform(
                X_train_processed[[col]]
            ).ravel()
            
            if X_val_processed is not None:
                X_val_processed[col] = cat_imputer.transform(
                    X_val_processed[[col]]
                ).ravel()
            
            X_test_processed[col] = cat_imputer.transform(
                X_test_processed[[col]]
            ).ravel()
            
            # 标签编码 - 处理未见过的标签
            le = LabelEncoder()
            X_train_processed[col] = le.fit_transform(
                X_train_processed[col].astype(str)
            )
            
            # 为验证集和测试集处理未见过的标签
            def safe_transform(encoder, data):
                """安全地转换数据，将未见过的标签映射为-1"""
                data_str = data.astype(str)
                # 创建一个映射，未见过的标签设为-1
                result = np.full(len(data_str), -1, dtype=int)
                
                # 对于已知的标签，使用正常的转换
                known_mask = np.isin(data_str, encoder.classes_)
                if known_mask.any():
                    result[known_mask] = encoder.transform(data_str[known_mask])
                
                return result
            
            if X_val_processed is not None:
                X_val_processed[col] = safe_transform(le, X_val_processed[col])
            
            X_test_processed[col] = safe_transform(le, X_test_processed[col])
            
            encoders[col] = {'imputer': cat_imputer, 'encoder': le}
        
        print(f"✓ 分类型特征处理完成")
    
    # 验证处理结果
    print(f"\n处理结果验证:")
    print(f"训练集缺失值: {X_train_processed.isnull().sum().sum()}")
    if X_val_processed is not None:
        print(f"验证集缺失值: {X_val_processed.isnull().sum().sum()}")
    print(f"测试集缺失值: {X_test_processed.isnull().sum().sum()}")
    
    return X_train_processed, X_val_processed, X_test_processed, encoders

# 执行特征预处理
X_train_processed, X_val_processed, X_test_processed, encoders = preprocess_features(
    X_train, X_val, X_test
)

# 正确的特征选择方法
def feature_selection_unbalanced(X_train, y_train, n_features=50, method='lightgbm'):
    """
    在不平衡数据上进行特征选择（仅使用训练集）
    """
    print(f"=== 特征选择 ({method}) ===")
    print(f"训练集形状: {X_train.shape}")
    print(f"目标特征数: {n_features}")
    print(f"类别分布: {dict(y_train.value_counts())}")
    
    if method == 'lightgbm':
        # 使用LightGBM进行特征选择
        lgb_params = {
            'objective': 'binary',
            'metric': 'auc',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'is_unbalance': True,  # 关键：处理不平衡数据
            'verbose': -1,
            'random_state': RANDOM_STATE,
            'force_row_wise': True
        }
        
        # 使用交叉验证获取稳定的特征重要性
        skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=RANDOM_STATE)
        feature_importance_scores = []
        cv_scores = []
        
        print("\n开始5折交叉验证...")
        for fold, (train_idx, val_idx) in enumerate(skf.split(X_train, y_train)):
            X_fold_train = X_train.iloc[train_idx]
            y_fold_train = y_train.iloc[train_idx]
            X_fold_val = X_train.iloc[val_idx]
            y_fold_val = y_train.iloc[val_idx]
            
            # 训练模型
            train_data = lgb.Dataset(X_fold_train, label=y_fold_train)
            val_data = lgb.Dataset(X_fold_val, label=y_fold_val, reference=train_data)
            
            model = lgb.train(
                lgb_params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=1000,
                callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
            )
            
            # 获取特征重要性
            importance = model.feature_importance(importance_type='gain')
            feature_importance_scores.append(importance)
            
            # 计算验证分数
            y_pred = model.predict(X_fold_val, num_iteration=model.best_iteration)
            auc_score = roc_auc_score(y_fold_val, y_pred)
            cv_scores.append(auc_score)
            
            print(f"  第 {fold + 1} 折 AUC: {auc_score:.4f}")
        
        # 计算平均特征重要性
        avg_importance = np.mean(feature_importance_scores, axis=0)
        std_importance = np.std(feature_importance_scores, axis=0)
        
        # 创建特征重要性DataFrame
        feature_importance_df = pd.DataFrame({
            'feature': X_train.columns,
            'importance_mean': avg_importance,
            'importance_std': std_importance
        }).sort_values('importance_mean', ascending=False)
        
        # 选择top特征
        selected_features = feature_importance_df.head(n_features)['feature'].tolist()
        
        print(f"\n交叉验证平均AUC: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")
        print(f"✓ 选择了 {len(selected_features)} 个特征")
        
        return selected_features, feature_importance_df, cv_scores
    
    elif method == 'statistical':
        # 使用统计方法
        selector = SelectKBest(score_func=f_classif, k=n_features)
        selector.fit(X_train, y_train)
        selected_features = X_train.columns[selector.get_support()].tolist()
        
        feature_scores_df = pd.DataFrame({
            'feature': X_train.columns,
            'score': selector.scores_
        }).sort_values('score', ascending=False)
        
        print(f"✓ 使用F检验选择了 {len(selected_features)} 个特征")
        
        return selected_features, feature_scores_df, None
    
    else:
        raise ValueError(f"不支持的特征选择方法: {method}")

# 执行特征选择
selected_features, feature_importance_df, cv_scores = feature_selection_unbalanced(
    X_train_processed, y_train, n_features=50, method='lightgbm'
)

print(f"\n选择的特征:")
print(selected_features[:10])  # 显示前10个特征
print(f"... 还有 {len(selected_features) - 10} 个特征")

# 准备最终数据集
print("=== 准备最终数据集 ===")

# 使用选定的特征
X_train_final = X_train_processed[selected_features]
X_test_final = X_test_processed[selected_features]

if X_val_processed is not None:
    X_val_final = X_val_processed[selected_features]
else:
    X_val_final = None

print(f"最终训练集形状: {X_train_final.shape}")
if X_val_final is not None:
    print(f"最终验证集形状: {X_val_final.shape}")
print(f"最终测试集形状: {X_test_final.shape}")

# 显示特征重要性Top 10
print(f"\nTop 10 重要特征:")
display(feature_importance_df.head(10))

# 模型训练函数
def train_and_evaluate_model(X_train, y_train, X_val, y_val, X_test, y_test, model_type='lightgbm'):
    """
    训练和评估模型
    """
    print(f"=== 模型训练与评估 ({model_type}) ===")
    
    if model_type == 'lightgbm':
        # LightGBM参数
        lgb_params = {
            'objective': 'binary',
            'metric': 'auc',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'is_unbalance': True,  # 处理不平衡数据
            'verbose': -1,
            'random_state': RANDOM_STATE,
            'force_row_wise': True
        }
        
        # 准备数据
        train_data = lgb.Dataset(X_train, label=y_train)
        
        if X_val is not None:
            val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
            valid_sets = [train_data, val_data]
            valid_names = ['train', 'valid']
        else:
            valid_sets = [train_data]
            valid_names = ['train']
        
        # 训练模型
        print("开始训练LightGBM模型...")
        model = lgb.train(
            lgb_params,
            train_data,
            valid_sets=valid_sets,
            valid_names=valid_names,
            num_boost_round=2000,
            callbacks=[
                lgb.early_stopping(100),
                lgb.log_evaluation(100)
            ]
        )
        
        print(f"✓ 模型训练完成，最佳迭代: {model.best_iteration}")
        
        # 预测
        y_train_pred = model.predict(X_train, num_iteration=model.best_iteration)
        y_test_pred = model.predict(X_test, num_iteration=model.best_iteration)
        
        if X_val is not None:
            y_val_pred = model.predict(X_val, num_iteration=model.best_iteration)
        else:
            y_val_pred = None
    
    elif model_type == 'logistic':
        # 逻辑回归
        model = LogisticRegression(
            class_weight='balanced',  # 处理不平衡数据
            random_state=RANDOM_STATE,
            max_iter=1000
        )
        
        print("开始训练逻辑回归模型...")
        model.fit(X_train, y_train)
        
        # 预测概率
        y_train_pred = model.predict_proba(X_train)[:, 1]
        y_test_pred = model.predict_proba(X_test)[:, 1]
        
        if X_val is not None:
            y_val_pred = model.predict_proba(X_val)[:, 1]
        else:
            y_val_pred = None
        
        print("✓ 逻辑回归模型训练完成")
    
    elif model_type == 'random_forest':
        # 随机森林
        model = RandomForestClassifier(
            n_estimators=200,
            class_weight='balanced',  # 处理不平衡数据
            random_state=RANDOM_STATE,
            n_jobs=-1
        )
        
        print("开始训练随机森林模型...")
        model.fit(X_train, y_train)
        
        # 预测概率
        y_train_pred = model.predict_proba(X_train)[:, 1]
        y_test_pred = model.predict_proba(X_test)[:, 1]
        
        if X_val is not None:
            y_val_pred = model.predict_proba(X_val)[:, 1]
        else:
            y_val_pred = None
        
        print("✓ 随机森林模型训练完成")
    
    else:
        raise ValueError(f"不支持的模型类型: {model_type}")
    
    return model, y_train_pred, y_val_pred, y_test_pred

# 训练多个模型进行对比
models = {}
predictions = {}

for model_type in ['lightgbm', 'logistic', 'random_forest']:
    print(f"\n{'='*50}")
    model, y_train_pred, y_val_pred, y_test_pred = train_and_evaluate_model(
        X_train_final, y_train, X_val_final, y_val, X_test_final, y_test, 
        model_type=model_type
    )
    
    models[model_type] = model
    predictions[model_type] = {
        'train': y_train_pred,
        'val': y_val_pred,
        'test': y_test_pred
    }

print(f"\n✓ 所有模型训练完成")

# 模型评估函数
def evaluate_model_performance(y_true, y_pred, dataset_name, model_name):
    """
    评估模型性能
    """
    # 计算各种指标
    auc = roc_auc_score(y_true, y_pred)
    pr_auc = average_precision_score(y_true, y_pred)
    
    # 找到最佳阈值
    fpr, tpr, thresholds = roc_curve(y_true, y_pred)
    optimal_idx = np.argmax(tpr - fpr)
    optimal_threshold = thresholds[optimal_idx]
    
    # 使用最佳阈值计算分类指标
    y_pred_binary = (y_pred >= optimal_threshold).astype(int)
    
    from sklearn.metrics import precision_score, recall_score
    precision = precision_score(y_true, y_pred_binary)
    recall = recall_score(y_true, y_pred_binary)
    f1 = f1_score(y_true, y_pred_binary)
    accuracy = accuracy_score(y_true, y_pred_binary)
    
    return {
        'model': model_name,
        'dataset': dataset_name,
        'auc': auc,
        'pr_auc': pr_auc,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'accuracy': accuracy,
        'optimal_threshold': optimal_threshold
    }

# 评估所有模型
print("=== 模型性能评估 ===")

evaluation_results = []

for model_name, preds in predictions.items():
    # 训练集评估
    train_metrics = evaluate_model_performance(
        y_train, preds['train'], 'train', model_name
    )
    evaluation_results.append(train_metrics)
    
    # 验证集评估（如果有）
    if preds['val'] is not None:
        val_metrics = evaluate_model_performance(
            y_val, preds['val'], 'validation', model_name
        )
        evaluation_results.append(val_metrics)
    
    # 测试集评估
    test_metrics = evaluate_model_performance(
        y_test, preds['test'], 'test', model_name
    )
    evaluation_results.append(test_metrics)

# 转换为DataFrame
results_df = pd.DataFrame(evaluation_results)

print("\n模型性能对比:")
display(results_df.round(4))

# 找出最佳模型（基于测试集AUC）
test_results = results_df[results_df['dataset'] == 'test']
best_model_name = test_results.loc[test_results['auc'].idxmax(), 'model']
best_auc = test_results['auc'].max()

print(f"\n🏆 最佳模型: {best_model_name} (测试集AUC: {best_auc:.4f})")

# 绘制ROC曲线和PR曲线
def plot_model_curves(y_true, predictions_dict, dataset_name='Test'):
    """
    绘制ROC曲线和PR曲线
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # ROC曲线
    ax1.plot([0, 1], [0, 1], 'k--', alpha=0.5, label='Random')
    
    for model_name, y_pred in predictions_dict.items():
        fpr, tpr, _ = roc_curve(y_true, y_pred)
        auc = roc_auc_score(y_true, y_pred)
        ax1.plot(fpr, tpr, label=f'{model_name} (AUC = {auc:.3f})', linewidth=2)
    
    ax1.set_xlabel('False Positive Rate')
    ax1.set_ylabel('True Positive Rate')
    ax1.set_title(f'ROC Curves - {dataset_name} Set')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # PR曲线
    baseline = y_true.sum() / len(y_true)  # 正样本比例
    ax2.axhline(y=baseline, color='k', linestyle='--', alpha=0.5, label=f'Baseline ({baseline:.3f})')
    
    for model_name, y_pred in predictions_dict.items():
        precision, recall, _ = precision_recall_curve(y_true, y_pred)
        pr_auc = average_precision_score(y_true, y_pred)
        ax2.plot(recall, precision, label=f'{model_name} (PR-AUC = {pr_auc:.3f})', linewidth=2)
    
    ax2.set_xlabel('Recall')
    ax2.set_ylabel('Precision')
    ax2.set_title(f'Precision-Recall Curves - {dataset_name} Set')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# 绘制测试集的ROC和PR曲线
test_predictions = {name: preds['test'] for name, preds in predictions.items()}
plot_model_curves(y_test, test_predictions, 'Test')

# 如果有验证集，也绘制验证集的曲线
if y_val is not None:
    val_predictions = {name: preds['val'] for name, preds in predictions.items() if preds['val'] is not None}
    plot_model_curves(y_val, val_predictions, 'Validation')

# 特征重要性可视化
def plot_feature_importance(feature_importance_df, top_n=20):
    """
    绘制特征重要性
    """
    plt.figure(figsize=(10, 8))
    
    top_features = feature_importance_df.head(top_n)
    
    plt.barh(range(len(top_features)), top_features['importance_mean'])
    plt.yticks(range(len(top_features)), top_features['feature'])
    plt.xlabel('特征重要性')
    plt.title(f'Top {top_n} 特征重要性')
    plt.gca().invert_yaxis()
    
    # 添加数值标签
    for i, v in enumerate(top_features['importance_mean']):
        plt.text(v + max(top_features['importance_mean']) * 0.01, i, f'{v:.0f}', 
                va='center', fontsize=9)
    
    plt.tight_layout()
    plt.show()

# 绘制特征重要性
print("=== 特征重要性分析 ===")
plot_feature_importance(feature_importance_df, top_n=20)

# 显示详细的特征重要性信息
print("\nTop 20 特征详细信息:")
display(feature_importance_df.head(20))

# 概率校准分析
def plot_calibration_curve(y_true, y_prob, model_name, n_bins=10):
    """
    绘制概率校准曲线
    """
    fraction_of_positives, mean_predicted_value = calibration_curve(
        y_true, y_prob, n_bins=n_bins
    )
    
    plt.figure(figsize=(8, 6))
    plt.plot([0, 1], [0, 1], 'k--', label='Perfect calibration')
    plt.plot(mean_predicted_value, fraction_of_positives, 'o-', 
             label=f'{model_name}', linewidth=2, markersize=8)
    
    plt.xlabel('Mean Predicted Probability')
    plt.ylabel('Fraction of Positives')
    plt.title(f'Calibration Curve - {model_name}')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
    
    return fraction_of_positives, mean_predicted_value

# 分析最佳模型的校准情况
print(f"=== 最佳模型 ({best_model_name}) 校准分析 ===")
best_test_pred = predictions[best_model_name]['test']
plot_calibration_curve(y_test, best_test_pred, best_model_name)

# 业务应用：不同阈值下的业务指标
def business_metrics_analysis(y_true, y_prob, thresholds=None):
    """
    分析不同阈值下的业务指标
    """
    if thresholds is None:
        thresholds = np.arange(0.1, 0.9, 0.1)
    
    results = []
    
    for threshold in thresholds:
        y_pred_binary = (y_prob >= threshold).astype(int)
        
        # 计算混淆矩阵
        tn, fp, fn, tp = confusion_matrix(y_true, y_pred_binary).ravel()
        
        # 计算业务指标
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        
        # 假设业务成本
        # FP成本：误判正常客户的成本
        # FN成本：漏判逾期客户的成本
        fp_cost = 100  # 误判一个正常客户的成本
        fn_cost = 1000  # 漏判一个逾期客户的成本
        
        total_cost = fp * fp_cost + fn * fn_cost
        
        results.append({
            'threshold': threshold,
            'precision': precision,
            'recall': recall,
            'specificity': specificity,
            'f1': 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0,
            'tp': tp,
            'fp': fp,
            'tn': tn,
            'fn': fn,
            'total_cost': total_cost
        })
    
    return pd.DataFrame(results)

# 业务指标分析
print(f"\n=== 业务应用分析 ===")
business_df = business_metrics_analysis(y_test, best_test_pred)

print("不同阈值下的业务指标:")
display(business_df.round(3))

# 找到最优业务阈值（最小化总成本）
optimal_business_idx = business_df['total_cost'].idxmin()
optimal_business_threshold = business_df.loc[optimal_business_idx, 'threshold']
min_cost = business_df.loc[optimal_business_idx, 'total_cost']

print(f"\n💰 最优业务阈值: {optimal_business_threshold:.2f}")
print(f"💰 最小总成本: {min_cost:,.0f}")

# 可视化阈值分析
plt.figure(figsize=(12, 8))

plt.subplot(2, 2, 1)
plt.plot(business_df['threshold'], business_df['precision'], 'o-', label='Precision')
plt.plot(business_df['threshold'], business_df['recall'], 's-', label='Recall')
plt.plot(business_df['threshold'], business_df['f1'], '^-', label='F1-Score')
plt.axvline(x=optimal_business_threshold, color='red', linestyle='--', alpha=0.7, label='Optimal')
plt.xlabel('Threshold')
plt.ylabel('Score')
plt.title('Performance Metrics vs Threshold')
plt.legend()
plt.grid(True, alpha=0.3)

plt.subplot(2, 2, 2)
plt.plot(business_df['threshold'], business_df['total_cost'], 'o-', color='red')
plt.axvline(x=optimal_business_threshold, color='red', linestyle='--', alpha=0.7, label='Optimal')
plt.xlabel('Threshold')
plt.ylabel('Total Cost')
plt.title('Business Cost vs Threshold')
plt.legend()
plt.grid(True, alpha=0.3)

plt.subplot(2, 2, 3)
plt.plot(business_df['threshold'], business_df['fp'], 'o-', label='False Positives', color='orange')
plt.plot(business_df['threshold'], business_df['fn'], 's-', label='False Negatives', color='red')
plt.axvline(x=optimal_business_threshold, color='red', linestyle='--', alpha=0.7, label='Optimal')
plt.xlabel('Threshold')
plt.ylabel('Count')
plt.title('False Positives & False Negatives vs Threshold')
plt.legend()
plt.grid(True, alpha=0.3)

plt.subplot(2, 2, 4)
plt.plot(business_df['threshold'], business_df['tp'], 'o-', label='True Positives', color='green')
plt.plot(business_df['threshold'], business_df['tn'], 's-', label='True Negatives', color='blue')
plt.axvline(x=optimal_business_threshold, color='red', linestyle='--', alpha=0.7, label='Optimal')
plt.xlabel('Threshold')
plt.ylabel('Count')
plt.title('True Positives & True Negatives vs Threshold')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# 保存最佳模型和相关信息
print("=== 模型保存 ===")

# 准备保存的数据
model_package = {
    'best_model': models[best_model_name],
    'best_model_name': best_model_name,
    'selected_features': selected_features,
    'feature_importance_df': feature_importance_df,
    'encoders': encoders,
    'evaluation_results': results_df,
    'business_metrics': business_df,
    'optimal_threshold': optimal_business_threshold,
    'target_col': target_col,
    'random_state': RANDOM_STATE,
    'model_performance': {
        'test_auc': best_auc,
        'test_pr_auc': test_results.loc[test_results['auc'].idxmax(), 'pr_auc']
    }
}

# 保存模型包
joblib.dump(model_package, 'overdue_prediction_model.pkl')
print("✓ 模型已保存到 'overdue_prediction_model.pkl'")

# 保存预测结果
prediction_results = pd.DataFrame({
    'actual': y_test,
    'predicted_prob': best_test_pred,
    'predicted_binary': (best_test_pred >= optimal_business_threshold).astype(int)
})
prediction_results.to_csv('test_predictions.csv', index=False)
print("✓ 测试集预测结果已保存到 'test_predictions.csv'")

# 保存特征列表
with open('selected_features.txt', 'w', encoding='utf-8') as f:
    f.write(f'# 最终选择的{len(selected_features)}个特征\n')
    f.write(f'# 模型: {best_model_name}\n')
    f.write(f'# 测试集AUC: {best_auc:.4f}\n')
    f.write(f'# 最优阈值: {optimal_business_threshold:.3f}\n')
    f.write(f'# 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n\n')
    
    for i, feature in enumerate(selected_features, 1):
        importance = feature_importance_df[feature_importance_df['feature'] == feature]['importance_mean'].iloc[0]
        f.write(f'{i:2d}. {feature:<30} (重要性: {importance:.0f})\n')

print("✓ 特征列表已保存到 'selected_features.txt'")

# 模型使用示例
def predict_overdue_probability(model_package_path, new_data):
    """
    使用保存的模型进行预测
    
    Parameters:
    -----------
    model_package_path : str
        模型包路径
    new_data : pandas.DataFrame
        新数据
    
    Returns:
    --------
    predictions : dict
        预测结果
    """
    
    # 加载模型包
    model_package = joblib.load(model_package_path)
    
    model = model_package['best_model']
    selected_features = model_package['selected_features']
    optimal_threshold = model_package['optimal_threshold']
    
    # 特征选择
    X_new = new_data[selected_features]
    
    # 预测
    if hasattr(model, 'predict'):
        # LightGBM
        prob_predictions = model.predict(X_new)
    else:
        # sklearn模型
        prob_predictions = model.predict_proba(X_new)[:, 1]
    
    # 二分类预测
    binary_predictions = (prob_predictions >= optimal_threshold).astype(int)
    
    return {
        'probabilities': prob_predictions,
        'predictions': binary_predictions,
        'threshold': optimal_threshold
    }

# 使用示例
print("=== 模型使用示例 ===")

df_test = pd.read_excel('result_20250617_160247.xlsx')

# 使用测试集的前5个样本作为示例
sample_data = df_test.head(5)
sample_predictions = predict_overdue_probability('overdue_prediction_model.pkl', sample_data)

print("示例预测结果:")
sample_results = pd.DataFrame({
    'sample_id': range(1, 6),
    'actual': y_test.head(5).values,
    'predicted_prob': sample_predictions['probabilities'],
    'predicted_class': sample_predictions['predictions'],
    'risk_level': ['高风险' if p >= 0.7 else '中风险' if p >= 0.3 else '低风险' 
                  for p in sample_predictions['probabilities']]
})

display(sample_results)

# 特征数量与性能关系分析
def plot_features_vs_performance(evaluation_df, figsize=(12, 6)):
    """
    绘制特征数量与性能的关系图
    
    Args:
        evaluation_df: 评估结果DataFrame
        figsize: 图形大小
    """
    # 过滤掉基准模型
    df_filtered = evaluation_df[~evaluation_df['method'].str.contains('baseline')].copy()
    
    # 提取方法类型
    df_filtered['method_type'] = df_filtered['method'].str.extract(r'([^_]+)_\d+')
    
    plt.figure(figsize=figsize)
    
    # 为不同方法类型使用不同颜色和标记
    method_styles = {
        'lgb': {'color': 'blue', 'marker': 'o', 'label': 'LightGBM重要性'},
        'rfe': {'color': 'red', 'marker': 's', 'label': '递归特征消除'},
        'statistical': {'color': 'green', 'marker': '^', 'label': '统计特征选择'}
    }
    
    for method_type, style in method_styles.items():
        method_data = df_filtered[df_filtered['method_type'] == method_type]
        if not method_data.empty:
            plt.errorbar(method_data['n_features'], method_data['cv_mean'], 
                        yerr=method_data['cv_std'], 
                        **style, markersize=8, linewidth=2, capsize=5)
    
    # 添加基准线
    baseline_score = evaluation_df[evaluation_df['method'].str.contains('baseline')]['cv_mean'].iloc[0]
    plt.axhline(y=baseline_score, color='black', linestyle='--', alpha=0.7, 
                label=f'基准模型 (所有特征): {baseline_score:.4f}')
    
    # 设置标题和标签
    plt.title('特征数量与模型性能关系', fontsize=16, fontweight='bold')
    plt.xlabel('特征数量', fontsize=12)
    plt.ylabel('AUC得分', fontsize=12)
    
    # 添加图例
    plt.legend()
    
    # 添加网格
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('features_vs_performance.png', dpi=300, bbox_inches='tight')
    plt.show()

# 绘制特征数量与性能关系图
plot_features_vs_performance(evaluation_df)

# 生成总结报告
print("=" * 60)
print("           逾期概率预测模型 - 项目总结报告")
print("=" * 60)

print(f"\n📊 数据概况:")
print(f"   • 总样本数: {len(data):,}")
print(f"   • 原始特征数: {data.shape[1] - 1}")
print(f"   • 最终特征数: {len(selected_features)}")
print(f"   • 正样本比例: {y.mean():.2%}")
print(f"   • 训练集大小: {len(X_train_final):,}")
print(f"   • 测试集大小: {len(X_test_final):,}")

print(f"\n🎯 模型性能:")
print(f"   • 最佳模型: {best_model_name}")
print(f"   • 测试集AUC: {best_auc:.4f}")
test_pr_auc = test_results.loc[test_results['auc'].idxmax(), 'pr_auc']
print(f"   • 测试集PR-AUC: {test_pr_auc:.4f}")

# 获取最佳模型在最优阈值下的性能
optimal_metrics = business_df[business_df['threshold'] == optimal_business_threshold].iloc[0]
print(f"   • 最优阈值: {optimal_business_threshold:.3f}")
print(f"   • 精确率: {optimal_metrics['precision']:.3f}")
print(f"   • 召回率: {optimal_metrics['recall']:.3f}")
print(f"   • F1分数: {optimal_metrics['f1']:.3f}")

print(f"\n🔍 特征工程:")
print(f"   • 特征选择方法: LightGBM重要性 + 交叉验证")
print(f"   • 避免数据泄露: ✓ (仅在训练集上进行特征选择)")
print(f"   • 处理不平衡数据: ✓ (使用is_unbalance=True)")
print(f"   • 缺失值处理: ✓ (数值型用中位数，分类型用众数)")

print(f"\n💼 业务价值:")
print(f"   • 最小化业务成本: {min_cost:,.0f}")
print(f"   • 误判成本节约: 通过优化阈值减少误判")
print(f"   • 风险识别能力: 能够有效识别高风险客户")

print(f"\n📁 输出文件:")
print(f"   • overdue_prediction_model.pkl - 完整模型包")
print(f"   • selected_features.txt - 特征列表")
print(f"   • test_predictions.csv - 测试集预测结果")

print(f"\n⚠️  注意事项:")
print(f"   • 模型需要定期重训练以保持性能")
print(f"   • 特征稳定性需要持续监控")
print(f"   • 业务阈值可根据实际成本调整")
print(f"   • 建议进行A/B测试验证模型效果")

print(f"\n🎉 项目完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("=" * 60)

# 保存总结报告
report_content = f"""
逾期概率预测模型 - 项目总结报告
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

数据概况:
- 总样本数: {len(data):,}
- 原始特征数: {data.shape[1] - 1}
- 最终特征数: {len(selected_features)}
- 正样本比例: {y.mean():.2%}
- 训练集大小: {len(X_train_final):,}
- 测试集大小: {len(X_test_final):,}

模型性能:
- 最佳模型: {best_model_name}
- 测试集AUC: {best_auc:.4f}
- 测试集PR-AUC: {test_pr_auc:.4f}
- 最优阈值: {optimal_business_threshold:.3f}
- 精确率: {optimal_metrics['precision']:.3f}
- 召回率: {optimal_metrics['recall']:.3f}
- F1分数: {optimal_metrics['f1']:.3f}

特征工程:
- 特征选择方法: LightGBM重要性 + 交叉验证
- 避免数据泄露: ✓ (仅在训练集上进行特征选择)
- 处理不平衡数据: ✓ (使用is_unbalance=True)
- 缺失值处理: ✓ (数值型用中位数，分类型用众数)

业务价值:
- 最小化业务成本: {min_cost:,.0f}
- 误判成本节约: 通过优化阈值减少误判
- 风险识别能力: 能够有效识别高风险客户

输出文件:
- overdue_prediction_model.pkl - 完整模型包
- selected_features.txt - 特征列表
- test_predictions.csv - 测试集预测结果

注意事项:
- 模型需要定期重训练以保持性能
- 特征稳定性需要持续监控
- 业务阈值可根据实际成本调整
- 建议进行A/B测试验证模型效果
"""

with open('model_summary_report.txt', 'w', encoding='utf-8') as f:
    f.write(report_content)

print("\n✓ 总结报告已保存到 'model_summary_report.txt'")
print("\n🎊 逾期概率预测模型项目完成！")